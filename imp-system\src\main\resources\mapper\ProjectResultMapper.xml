<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ProjectResultMapper">

    <resultMap type="com.qmqb.imp.system.domain.ProjectResult" id="ProjectResultResult">
        <result property="id" column="id"/>
        <result property="businessTypeId" column="business_type_id"/>
        <result property="resultCode" column="result_code"/>
        <result property="resultType" column="result_type"/>
        <result property="projectTaskName" column="project_task_name"/>
        <result property="priorityLevel" column="priority_level"/>
        <result property="status" column="status"/>
        <result property="milestoneRequirements" column="milestone_requirements"/>
        <result property="milestoneDevelopment" column="milestone_development"/>
        <result property="milestoneTest" column="milestone_test"/>
        <result property="milestoneOnline" column="milestone_online"/>
        <result property="requirementsProgress" column="requirements_progress"/>
        <result property="developmentProgress" column="development_progress"/>
        <result property="testProgress" column="test_progress"/>
        <result property="devTeams" column="dev_teams"/>
        <result property="testTeams" column="test_teams"/>
        <result property="productManagers" column="product_managers"/>
        <result property="devManpower" column="dev_manpower"/>
        <result property="testManpower" column="test_manpower"/>
        <result property="devWorkload" column="dev_workload"/>
        <result property="testWorkload" column="test_workload"/>
        <result property="requirementBackground" column="requirement_background"/>
        <result property="remark" column="remark"/>
        <result property="projectManagers" column="project_managers"/>
        <result property="completionTime" column="completion_time"/>
        <result property="archiveFlag" column="archive_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 关联查询的结果映射 -->
    <resultMap type="com.qmqb.imp.system.domain.vo.ProjectResultVo" id="ProjectResultVoResult">
        <result property="id" column="id"/>
        <result property="businessTypeId" column="business_type_id"/>
        <result property="resultCode" column="result_code"/>
        <result property="resultType" column="result_type"/>
        <result property="projectTaskName" column="project_task_name"/>
        <result property="priorityLevel" column="priority_level"/>
        <result property="status" column="status"/>
        <result property="milestoneRequirements" column="milestone_requirements"/>
        <result property="milestoneDevelopment" column="milestone_development"/>
        <result property="milestoneTest" column="milestone_test"/>
        <result property="milestoneOnline" column="milestone_online"/>
        <result property="requirementsProgress" column="requirements_progress"/>
        <result property="developmentProgress" column="development_progress"/>
        <result property="testProgress" column="test_progress"/>
        <result property="devTeams" column="dev_teams"/>
        <result property="testTeams" column="test_teams"/>
        <result property="productManagers" column="product_managers"/>
        <result property="devManpower" column="dev_manpower"/>
        <result property="testManpower" column="test_manpower"/>
        <result property="devWorkload" column="dev_workload"/>
        <result property="testWorkload" column="test_workload"/>
        <result property="requirementBackground" column="requirement_background"/>
        <result property="remark" column="remark"/>
        <result property="businessCategoryMajor" column="business_category_major"/>
        <result property="businessCategoryMinor" column="business_category_minor"/>
        <result property="projectManagers" column="project_managers"/>
        <result property="completionTime" column="completion_time"/>
        <result property="archiveFlag" column="archive_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="businessTypeName" column="business_type_name"/>
        <result property="storyCount" column="story_count"/>
    </resultMap>

    <!-- 基础查询SQL -->
    <sql id="selectProjectResultVo">
        select pr.id, pr.business_type_id, pr.result_code, pr.result_type, pr.project_task_name, pr.priority_level, pr.status,
               pr.milestone_requirements, pr.milestone_development, pr.milestone_test, pr.milestone_online,
               pr.requirements_progress, pr.development_progress, pr.test_progress,
               pr.dev_teams, pr.test_teams, pr.product_managers, pr.dev_manpower, pr.test_manpower,
               pr.dev_workload, pr.test_workload, pr.requirement_background, pr.remark, pr.project_managers,
               pr.completion_time, pr.archive_flag, pr.create_by, pr.create_time, pr.update_by, pr.update_time,
               bt.business_type_name, bt.sort as business_type_sort,
               bt.business_category_major, bt.business_category_minor,
               COALESCE(sr.story_count, 0) as story_count,
               COALESCE(major_dict.dict_sort, 999) as business_category_major_sort,
               COALESCE(minor_dict.dict_sort, 999) as business_category_minor_sort
        from tb_project_result pr
        left join tb_business_type bt on pr.business_type_id = bt.id and bt.del_flag = 0
        left join (
            select result_id, count(*) as story_count
            from tb_story_result
            where del_flag = 0 and result_id is not null
            group by result_id
        ) sr on pr.id = sr.result_id
        left join sys_dict_data major_dict on bt.business_category_major = major_dict.dict_value
            and major_dict.dict_type = 'project_outcome_business_category_major'
            and major_dict.status = '0'
        left join sys_dict_data minor_dict on bt.business_category_minor = minor_dict.dict_value
            and minor_dict.dict_type = 'project_outcome_business_category_minor'
            and minor_dict.status = '0'
        where pr.del_flag = 0
    </sql>

    <!-- 分页查询项目成果列表（关联业务类型表） -->
    <select id="selectVoPageWithBusinessType" resultMap="ProjectResultVoResult">
        <include refid="selectProjectResultVo"/>
        <!-- 业务大类查询 -->
        <if test="bo.businessCategoryMajor != null and bo.businessCategoryMajor != ''">
            and bt.business_category_major = #{bo.businessCategoryMajor}
        </if>
        <!-- 业务小类查询 -->
        <if test="bo.businessCategoryMinor != null and bo.businessCategoryMinor != ''">
            and bt.business_category_minor = #{bo.businessCategoryMinor}
        </if>
        <!-- 业务类型ID查询 -->
        <if test="bo.businessTypeId != null">
            and pr.business_type_id = #{bo.businessTypeId}
        </if>
        <!-- 成果编码模糊查询 -->
        <if test="bo.resultCode != null and bo.resultCode != ''">
            and pr.result_code like concat('%', #{bo.resultCode}, '%')
        </if>
        <!-- 状态查询 -->
        <if test="bo.status != null and bo.status != ''">
            and pr.status = #{bo.status}
        </if>
        <!-- 项目经理模糊查询 -->
        <if test="bo.projectManagers != null and bo.projectManagers != ''">
            and pr.project_managers like concat('%', #{bo.projectManagers}, '%')
        </if>
        <!-- 项目/任务名称模糊查询 -->
        <if test="bo.projectTaskName != null and bo.projectTaskName != ''">
            and pr.project_task_name like concat('%', #{bo.projectTaskName}, '%')
        </if>
        <!-- 成果类型查询 -->
        <if test="bo.resultType != null and bo.resultType != ''">
            and pr.result_type = #{bo.resultType}
        </if>
        <!-- 优先级查询 -->
        <if test="bo.priorityLevel != null and bo.priorityLevel != ''">
            and pr.priority_level = #{bo.priorityLevel}
        </if>
        <!-- 创建时间范围查询 -->
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and pr.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        </if>
        <!-- 完成时间范围查询 -->
        <if test="bo.completionTimeStart != null and bo.completionTimeEnd != null">
            and pr.completion_time between #{bo.completionTimeStart} and #{bo.completionTimeEnd}
        </if>
        <!-- 归档标志查询 -->
        <choose>
            <when test="bo.archiveFlag != null">
                and pr.archive_flag = #{bo.archiveFlag}
            </when>
            <otherwise>
                <!-- 默认不查询已归档的数据 -->
                and pr.archive_flag = 0
            </otherwise>
        </choose>
        <!-- 排序逻辑 -->
        <choose>
            <when test="orderByColumn != null and orderByColumn != ''">
                <!-- 自定义排序 -->
                ORDER BY
                <foreach collection="orderByColumns" item="column" index="index" separator=",">
                    <choose>
                        <when test="column == 'priority_level' or column == 'priorityLevel'">
                            CASE pr.priority_level WHEN 'P1' THEN 1 WHEN 'P2' THEN 2 WHEN 'P3' THEN 3 ELSE 4 END
                            <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </when>
                        <when test="column == 'update_time' or column == 'updateTime'">
                            pr.update_time <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </when>
                        <when test="column == 'completion_time' or column == 'completionTime'">
                            pr.completion_time <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </when>
                        <when test="column == 'create_time' or column == 'createTime'">
                            pr.create_time <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </when>
                        <when test="column == 'story_count' or column == 'storyCount'">
                            COALESCE(sr.story_count, 0) <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </when>
                        <when test="column == 'create_by' or column == 'createBy'">
                            CASE pr.create_by
                                WHEN 'zhengjingxia' THEN <if test="isAscArray[index] == 'desc'">4</if><if test="isAscArray[index] != 'desc'">1</if>
                                WHEN 'lichonggao' THEN <if test="isAscArray[index] == 'desc'">3</if><if test="isAscArray[index] != 'desc'">2</if>
                                WHEN 'hujiaxin' THEN <if test="isAscArray[index] == 'desc'">2</if><if test="isAscArray[index] != 'desc'">3</if>
                                WHEN 'huangjinyuan' THEN <if test="isAscArray[index] == 'desc'">1</if><if test="isAscArray[index] != 'desc'">4</if>
                                ELSE <if test="isAscArray[index] == 'desc'">5</if><if test="isAscArray[index] != 'desc'">5</if>
                            END
                        </when>
                        <when test="column == 'business_category_major' or column == 'businessCategoryMajor'">
                            COALESCE(major_dict.dict_sort, 999) <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </when>
                        <when test="column == 'business_category_minor' or column == 'businessCategoryMinor'">
                            COALESCE(minor_dict.dict_sort, 999) <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </when>
                        <otherwise>
                            pr.${column} <if test="isAscArray[index] == 'desc'"> DESC</if>
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>
                <!-- 默认排序：按创建时间倒序排序（最新的在前面） -->
                ORDER BY pr.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询项目成果列表（关联业务类型表） -->
    <select id="selectVoListWithBusinessType" resultMap="ProjectResultVoResult">
        <include refid="selectProjectResultVo"/>
        <!-- 业务大类查询 -->
        <if test="bo.businessCategoryMajor != null and bo.businessCategoryMajor != ''">
            and bt.business_category_major = #{bo.businessCategoryMajor}
        </if>
        <!-- 业务小类查询 -->
        <if test="bo.businessCategoryMinor != null and bo.businessCategoryMinor != ''">
            and bt.business_category_minor = #{bo.businessCategoryMinor}
        </if>
        <!-- 业务类型ID查询 -->
        <if test="bo.businessTypeId != null">
            and pr.business_type_id = #{bo.businessTypeId}
        </if>
        <!-- 成果编码模糊查询 -->
        <if test="bo.resultCode != null and bo.resultCode != ''">
            and pr.result_code like concat('%', #{bo.resultCode}, '%')
        </if>
        <!-- 状态查询 -->
        <if test="bo.status != null and bo.status != ''">
            and pr.status = #{bo.status}
        </if>
        <!-- 项目经理模糊查询 -->
        <if test="bo.projectManagers != null and bo.projectManagers != ''">
            and pr.project_managers like concat('%', #{bo.projectManagers}, '%')
        </if>
        <!-- 项目/任务名称模糊查询 -->
        <if test="bo.projectTaskName != null and bo.projectTaskName != ''">
            and pr.project_task_name like concat('%', #{bo.projectTaskName}, '%')
        </if>
        <!-- 成果类型查询 -->
        <if test="bo.resultType != null and bo.resultType != ''">
            and pr.result_type = #{bo.resultType}
        </if>
        <!-- 优先级查询 -->
        <if test="bo.priorityLevel != null and bo.priorityLevel != ''">
            and pr.priority_level = #{bo.priorityLevel}
        </if>
        <!-- 创建时间范围查询 -->
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and pr.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        </if>
        <!-- 完成时间范围查询 -->
        <if test="bo.completionTimeStart != null and bo.completionTimeEnd != null">
            and pr.completion_time between #{bo.completionTimeStart} and #{bo.completionTimeEnd}
        </if>
        <!-- 归档标志查询 -->
        <choose>
            <when test="bo.archiveFlag != null">
                and pr.archive_flag = #{bo.archiveFlag}
            </when>
            <otherwise>
                <!-- 默认不查询已归档的数据 -->
                and pr.archive_flag = 0
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询项目成果详情（关联业务类型表） -->
    <select id="selectVoByIdWithBusinessType" resultMap="ProjectResultVoResult">
        <include refid="selectProjectResultVo"/>
        and pr.id = #{id}
    </select>

    <!-- 查询信息不完整的项目成果列表（分批查询） -->
    <select id="selectIncompleteProjectResults" resultType="java.util.Map">
        SELECT
            pr.id,
            pr.result_code,
            pr.project_task_name,
            pr.result_type,
            pr.milestone_requirements,
            pr.milestone_development,
            pr.milestone_test,
            pr.milestone_online,
            pr.requirements_progress,
            pr.development_progress,
            pr.test_progress,
            pr.dev_teams,
            pr.test_teams,
            pr.product_managers,
            pr.dev_manpower,
            pr.test_manpower,
            pr.dev_workload,
            pr.test_workload,
            -- 直接在SQL中计算缺失字段
            CONCAT_WS(',',
                CASE WHEN (pr.milestone_requirements IS NULL
                          AND pr.milestone_development IS NULL
                          AND pr.milestone_test IS NULL
                          AND pr.milestone_online IS NULL) THEN '项目里程碑' END,
                CASE WHEN (pr.requirements_progress IS NULL
                          AND pr.development_progress IS NULL
                          AND pr.test_progress IS NULL) THEN '任务进度' END,
                CASE WHEN ((pr.product_managers IS NULL OR TRIM(pr.product_managers) = '')
                          AND (pr.dev_teams IS NULL OR TRIM(pr.dev_teams) = '')
                          AND (pr.test_teams IS NULL OR TRIM(pr.test_teams) = '')) THEN '干系人' END,
                CASE WHEN ((pr.dev_manpower IS NULL OR pr.dev_manpower = 0 OR pr.dev_manpower = 0.00)
                          AND (pr.test_manpower IS NULL OR pr.test_manpower = 0 OR pr.test_manpower = 0.00)) THEN '投入人力' END,
                CASE WHEN ((pr.dev_workload IS NULL OR pr.dev_workload = 0 OR pr.dev_workload = 0.00)
                          AND (pr.test_workload IS NULL OR pr.test_workload = 0 OR pr.test_workload = 0.00)) THEN '工作量' END
            ) AS missing_fields
        FROM tb_project_result pr
        WHERE pr.result_type != '4'  -- 排除事项支撑类型
        AND (
            -- 项目里程碑：4个字段都为空才算空
            (pr.milestone_requirements IS NULL
             AND pr.milestone_development IS NULL
             AND pr.milestone_test IS NULL
             AND pr.milestone_online IS NULL)
            -- 任务进度：3个字段都为空才算空
            OR (pr.requirements_progress IS NULL
                AND pr.development_progress IS NULL
                AND pr.test_progress IS NULL)
            -- 干系人：3个字段都为空才算空（考虑空字符串）
            OR ((pr.product_managers IS NULL OR TRIM(pr.product_managers) = '')
                AND (pr.dev_teams IS NULL OR TRIM(pr.dev_teams) = '')
                AND (pr.test_teams IS NULL OR TRIM(pr.test_teams) = ''))
            -- 投入人力：2个字段都为空才算空（考虑0值）
            OR ((pr.dev_manpower IS NULL OR pr.dev_manpower = 0 OR pr.dev_manpower = 0.00)
                AND (pr.test_manpower IS NULL OR pr.test_manpower = 0 OR pr.test_manpower = 0.00))
            -- 工作量：2个字段都为空才算空（考虑0值）
            OR ((pr.dev_workload IS NULL OR pr.dev_workload = 0 OR pr.dev_workload = 0.00)
                AND (pr.test_workload IS NULL OR pr.test_workload = 0 OR pr.test_workload = 0.00))
        )
        ORDER BY pr.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询所有信息不完整的项目成果列表 -->
    <select id="selectAllIncompleteProjectResults" resultType="java.util.Map">
        SELECT
            pr.id,
            pr.result_code,
            pr.project_task_name,
            -- 项目里程碑
            pr.milestone_requirements,
            pr.milestone_development,
            pr.milestone_test,
            pr.milestone_online,
            -- 任务进度
            pr.requirements_progress,
            pr.development_progress,
            pr.test_progress,
            -- 干系人
            pr.dev_teams,
            pr.test_teams,
            pr.product_managers,
            -- 投入人力
            pr.dev_manpower,
            pr.test_manpower,
            -- 工作量
            pr.dev_workload,
            pr.test_workload
        FROM tb_project_result pr
        WHERE pr.result_type != '4'  -- 排除事项支撑类型
        AND (
            -- 项目里程碑：4个字段都为空才算空
            (pr.milestone_requirements IS NULL
             AND pr.milestone_development IS NULL
             AND pr.milestone_test IS NULL
             AND pr.milestone_online IS NULL)
            -- 任务进度：3个字段都为空才算空
            OR (pr.requirements_progress IS NULL
                AND pr.development_progress IS NULL
                AND pr.test_progress IS NULL)
            -- 干系人：3个字段都为空才算空（考虑空字符串）
            OR ((pr.product_managers IS NULL OR TRIM(pr.product_managers) = '')
                AND (pr.dev_teams IS NULL OR TRIM(pr.dev_teams) = '')
                AND (pr.test_teams IS NULL OR TRIM(pr.test_teams) = ''))
            -- 投入人力：2个字段都为空才算空（考虑0值）
            OR ((pr.dev_manpower IS NULL OR pr.dev_manpower = 0 OR pr.dev_manpower = 0.00)
                AND (pr.test_manpower IS NULL OR pr.test_manpower = 0 OR pr.test_manpower = 0.00))
            -- 工作量：2个字段都为空才算空（考虑0值）
            OR ((pr.dev_workload IS NULL OR pr.dev_workload = 0 OR pr.dev_workload = 0.00)
                AND (pr.test_workload IS NULL OR pr.test_workload = 0 OR pr.test_workload = 0.00))
        )
        ORDER BY pr.create_time DESC
    </select>

    <select id="selectVoByIds" resultType="com.qmqb.imp.system.domain.vo.ProjectResultVo">
        select pr.project_task_name,pr.product_managers,pr.milestone_online,tb.business_type_name,tb.business_manager,
               tb.business_category_major,tb.business_category_minor,pr.remark
        from tb_project_result pr
        left join tb_business_type tb on pr.business_type_id = tb.id
        where pr.id in
        <foreach collection="resultIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by
        tb.sort asc,
        CASE pr.priority_level WHEN 'P1' THEN 1 WHEN 'P2' THEN 2 WHEN 'P3' THEN 3 ELSE 4 END ASC,
        pr.milestone_online asc
    </select>

    <select id="listByDoingOrNotStart" resultType="com.qmqb.imp.system.domain.vo.ProjectResultVo">
        select pr.project_task_name,pr.result_code,tb.business_type_name,pr.id
        from tb_project_result pr
        left join tb_business_type tb on pr.business_type_id = tb.id
        where (pr.status = '1' or pr.status = '2')
        <if test="bo.params != null and bo.params != ''">
            and (
                    pr.project_task_name like concat('%',#{bo.params},'%')
                 or pr.result_code like concat('%',#{bo.params},'%')
                 or tb.business_type_name like concat('%',#{bo.params},'%')
                )
        </if>
    </select>

    <!-- 根据项目成果ID查询关联的需求列表 -->
    <select id="selectRelatedStoriesByResultId" resultType="com.qmqb.imp.system.domain.vo.RelatedStoryVo">
        select
            sr.id as storyId,
            sr.title as storyTitle,
            sr.product_name as productName,
            sr.product_id as productId
        from tb_story_result sr
        where sr.result_id = #{resultId}
          and sr.del_flag = 0
        order by sr.create_time desc
    </select>

</mapper>
