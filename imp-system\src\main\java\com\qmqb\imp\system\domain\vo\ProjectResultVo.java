package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目成果表视图对象 tb_project_result
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 业务类型ID，关联业务类型管理表主键
     */
    @ExcelProperty(value = "业务类型ID")
    private Long businessTypeId;

    /**
     * 成果编号
     */
    @ExcelProperty(value = "成果编号")
    private String resultCode;

    /**
     * 成果类型：1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它
     */
    @ExcelProperty(value = "成果类型")
    private String resultType;

    /**
     * 项目/任务名称，限制30字符
     */
    @ExcelProperty(value = "项目/任务名称")
    private String projectTaskName;

    /**
     * 优先级：P1/P2/P3
     */
    @ExcelProperty(value = "优先级")
    private String priorityLevel;

    /**
     * 状态：1未开始、2进行中、3已完成、4已取消
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 完成评审时间
     */
    @ExcelProperty(value = "完成评审时间")
    private Date milestoneRequirements;

    /**
     * 完成开发时间
     */
    @ExcelProperty(value = "完成开发时间")
    private Date milestoneDevelopment;

    /**
     * 完成测试验收时间
     */
    @ExcelProperty(value = "完成测试验收时间")
    private Date milestoneTest;

    /**
     * 完成上线时间
     */
    @ExcelProperty(value = "完成上线时间")
    private Date milestoneOnline;

    /**
     * 需求评审进度百分比
     */
    @ExcelProperty(value = "需求评审进度")
    private BigDecimal requirementsProgress;

    /**
     * 开发进度百分比
     */
    @ExcelProperty(value = "开发进度")
    private BigDecimal developmentProgress;

    /**
     * 测试验收进度百分比
     */
    @ExcelProperty(value = "测试验收进度")
    private BigDecimal testProgress;

    /**
     * 开发组，多个用逗号分隔
     */
    @ExcelProperty(value = "开发组")
    private String devTeams;

    /**
     * 测试组，多个用逗号分隔
     */
    @ExcelProperty(value = "测试组")
    private String testTeams;

    /**
     * 产品经理(需求创建人)，多个用逗号分隔
     */
    @ExcelProperty(value = "产品")
    private String productManagers;

    /**
     * 开发投入人力（人）
     */
    @ExcelProperty(value = "开发投入人力")
    private Integer devManpower;

    /**
     * 测试投入人力（人）
     */
    @ExcelProperty(value = "测试投入人力")
    private Integer testManpower;

    /**
     * 开发工作量（人日）
     */
    @ExcelProperty(value = "开发工作量")
    private BigDecimal devWorkload;

    /**
     * 测试工作量（人日）
     */
    @ExcelProperty(value = "测试工作量")
    private BigDecimal testWorkload;

    /**
     * 需求背景
     */
    @ExcelProperty(value = "需求背景")
    private String requirementBackground;

    /**
     * 备注信息，最多1000字符
     */
    @ExcelProperty(value = "备注信息")
    private String remark;

    /**
     * 所属业务大类：1国内、2海外
     */
    @ExcelProperty(value = "所属业务大类")
    private String businessCategoryMajor;

    /**
     * 所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它
     */
    @ExcelProperty(value = "所属业务小类")
    private String businessCategoryMinor;

    /**
     * 负责项目经理，多个用逗号分隔
     */
    @ExcelProperty(value = "负责项目经理")
    private String projectManagers;

    /**
     * 完成时间，仅状态为已完成时可填写
     */
    @ExcelProperty(value = "完成时间")
    private Date completionTime;

    /**
     * 归档标志：0未归档、1已归档
     */
    @ExcelProperty(value = "归档标志")
    private Integer archiveFlag;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 业务类型名称
     */
    @ExcelProperty(value = "业务类型名称")
    private String businessTypeName;

    /**
     * 关联的需求个数
     */
    @ExcelProperty(value = "关联的需求个数")
    private Integer storyCount;

    /**
     * 关联的需求列表
     */
    private List<RelatedStoryVo> relatedStories;

    /**
     * 业务负责人
     */
    @ExcelProperty(value = "业务负责人")
    private String businessManager;
}
